use crate::components::{
    layout::{Container, Grid, GridGap, Section},
    ui::{
        Badge, BadgeSize, Badge<PERSON>t, Button, ButtonSize, ButtonVariant, Card, CardBody,
        FranchiseBanner, Icon, IconType, LoadingSpinner, ProductImage,
    },
};
use crate::services::mock_data::{load_products_from_json, load_franchise_categories_from_json};
use crate::types::{Product, FranchiseCategory};
use crate::Route;
use yew::prelude::*;
use yew_router::prelude::*;

#[function_component(HomePage)]
pub fn home_page() -> Html {
    html! {
        <>
            <HeroSection />
            <FeaturedCategories />
            <FeaturedProducts />
            <AboutSection />
            <CallToAction />
        </>
    }
}

#[function_component(HeroSection)]
fn hero_section() -> Html {
    html! {
        <Section class="bg-gradient-anime text-white relative overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
            <Container class="relative z-10 text-center">
                <div class="max-w-4xl mx-auto">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
                        {"Nghệ thuật không giới hạn..."}
                    </h1>
                    <p class="text-xl md:text-2xl mb-8 text-gray-100 animate-slide-up">
                        {"Thỏa thích thể hiện cá tính tại Puka Studio!"}
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
                        <Link<Route> to={Route::Catalog}>
                            <Button variant={ButtonVariant::Secondary} size={ButtonSize::Large} class="w-full sm:w-auto">
                                <Icon icon_type={IconType::Search} class="mr-2 h-5 w-5" />
                                {"Tìm Hiểu Thêm"}
                            </Button>
                        </Link<Route>>
                        <a
                            href="https://www.youtube.com/@PukaStudio"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <Button variant={ButtonVariant::Outline} size={ButtonSize::Large} class="w-full sm:w-auto border-white text-white hover:bg-white hover:text-primary-600">
                                <Icon icon_type={IconType::Play} class="mr-2 h-5 w-5" />
                                {"Xem Video"}
                            </Button>
                        </a>
                    </div>
                </div>
            </Container>

            // Floating elements for anime aesthetic
            <div class="absolute top-20 left-10 opacity-20 float-animation">
                <Icon icon_type={IconType::Star} class="h-8 w-8" />
            </div>
            <div class="absolute top-40 right-20 opacity-20 float-animation" style="animation-delay: 1s;">
                <Icon icon_type={IconType::Heart} class="h-6 w-6" />
            </div>
            <div class="absolute bottom-20 left-20 opacity-20 float-animation" style="animation-delay: 2s;">
                <Icon icon_type={IconType::Star} class="h-10 w-10" />
            </div>
        </Section>
    }
}

#[function_component(FeaturedCategories)]
fn featured_categories() -> Html {
    let franchise_categories = use_state(|| Vec::<FranchiseCategory>::new());
    let loading = use_state(|| true);
    let error = use_state(|| None::<String>);

    // Load franchise categories from JSON
    {
        let franchise_categories = franchise_categories.clone();
        let loading = loading.clone();
        let error = error.clone();

        use_effect_with((), move |_| {
            wasm_bindgen_futures::spawn_local(async move {
                match load_franchise_categories_from_json().await {
                    Ok(loaded_categories) => {
                        // Filter to only featured categories, exclude "Unknown", and limit to 4 for display
                        let featured: Vec<FranchiseCategory> = loaded_categories
                            .into_iter()
                            .filter(|c| c.featured && c.name.to_lowercase() != "unknown")
                            .take(4)
                            .collect();
                        franchise_categories.set(featured);
                        loading.set(false);
                    }
                    Err(e) => {
                        error.set(Some(e));
                        loading.set(false);
                    }
                }
            });
            || ()
        });
    }

    // Helper function to get Vietnamese description for category type
    let get_category_description = |category_type: &str| -> &'static str {
        match category_type {
            "game" => "Nhân vật từ các game nổi tiếng",
            "anime" => "Trang phục từ anime và manga",
            "vtuber" => "Trang phục VTuber yêu thích",
            "vocaloid" => "Nhân vật Vocaloid kinh điển",
            _ => "Trang phục cosplay đa dạng",
        }
    };

    html! {
        <Section class="bg-white">
            <Container>
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        {"Các chủ đề thịnh hành"}
                    </h2>
                    <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                        {"Cùng PUKA hóa thân thành các nhân vật mà bạn yêu thích"}
                    </p>
                </div>

                if *loading {
                    <div class="text-center py-12">
                        <LoadingSpinner size={32} />
                        <p class="text-gray-500 mt-4">{"Đang tải chủ đề..."}</p>
                    </div>
                } else if let Some(err) = error.as_ref() {
                    <div class="text-center py-12">
                        <p class="text-red-500">{"Lỗi tải dữ liệu: "}{err}</p>
                    </div>
                } else if franchise_categories.is_empty() {
                    <div class="text-center py-12">
                        <p class="text-gray-500">{"Không có chủ đề nổi bật"}</p>
                    </div>
                } else {
                    <Grid cols={4} gap={GridGap::Large}>
                        { for franchise_categories.iter().map(|franchise| {
                            let description = get_category_description(&franchise.category_type);

                            // Create route to franchise-specific catalog
                            // Convert franchise name to URL-friendly format
                            let franchise_slug = franchise.name.to_lowercase().replace(" ", "-");
                            let route = Route::CatalogCategory {
                                category: franchise_slug
                            };

                            html! {
                                <Link<Route> to={route}>
                                    <Card hover={true} class="group cursor-pointer h-full">
                                        <FranchiseBanner
                                            franchise_name={franchise.name.clone()}
                                            category_type={franchise.category_type.clone()}
                                        >
                                            // Show product count badge
                                            <div class="absolute top-2 right-2 z-10">
                                                <Badge variant={BadgeVariant::Secondary} size={BadgeSize::Small}>
                                                    {format!("{} sản phẩm", franchise.product_count)}
                                                </Badge>
                                            </div>
                                        </FranchiseBanner>
                                        <CardBody>
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                                                {&franchise.name}
                                            </h3>
                                            <p class="text-gray-600 text-sm mb-2">
                                                {description}
                                            </p>
                                            <p class="text-primary-600 text-xs font-medium">
                                                {format!("{} trang phục có sẵn", franchise.product_count)}
                                            </p>
                                        </CardBody>
                                    </Card>
                                </Link<Route>>
                            }
                        }) }
                    </Grid>

                    // Add link to view all categories
                    <div class="text-center mt-12">
                        <Link<Route> to={Route::Catalog}>
                            <Button variant={ButtonVariant::Outline} size={ButtonSize::Large}>
                                {"Xem Tất Cả Chủ Đề"}
                            </Button>
                        </Link<Route>>
                    </div>
                }
            </Container>
        </Section>
    }
}

#[function_component(FeaturedProducts)]
fn featured_products() -> Html {
    let products = use_state(|| Vec::<Product>::new());
    let loading = use_state(|| true);
    let error = use_state(|| None::<String>);

    // Load products from JSON
    {
        let products = products.clone();
        let loading = loading.clone();
        let error = error.clone();

        use_effect_with((), move |_| {
            wasm_bindgen_futures::spawn_local(async move {
                match load_products_from_json().await {
                    Ok(loaded_products) => {
                        // Take only featured products, limit to 4
                        let featured: Vec<Product> = loaded_products
                            .into_iter()
                            .filter(|p| p.featured)
                            .take(4)
                            .collect();
                        products.set(featured);
                        loading.set(false);
                    }
                    Err(e) => {
                        error.set(Some(e));
                        loading.set(false);
                    }
                }
            });
            || ()
        });
    }

    html! {
        <Section class="bg-gray-50">
            <Container>
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        {"Được cập nhật liên tục"}
                    </h2>
                    <p class="text-xl text-gray-600 mb-2">
                        {"những nhân vật mới"}
                    </p>
                    <p class="text-gray-500">
                        {"Liên hệ shop để thêm tư vấn nhen"}
                    </p>
                </div>

                if *loading {
                    <div class="text-center py-12">
                        <LoadingSpinner size={32} />
                        <p class="text-gray-500 mt-4">{"Đang tải sản phẩm..."}</p>
                    </div>
                } else if let Some(err) = error.as_ref() {
                    <div class="text-center py-12">
                        <p class="text-red-500">{"Lỗi tải dữ liệu: "}{err}</p>
                    </div>
                } else if products.is_empty() {
                    <div class="text-center py-12">
                        <p class="text-gray-500">{"Không có sản phẩm nổi bật"}</p>
                    </div>
                } else {
                    <Grid cols={4} gap={GridGap::Large}>
                        { for products.iter().map(|product| {
                            // Get the first rental option for pricing
                            let price_text = if let Some(rental_option) = product.rental_options.first() {
                                format!("{}.000 ₫", rental_option.price / 1000)
                            } else {
                                "Liên hệ".to_string()
                            };

                            // Check availability (simplified - you might want more complex logic)
                            let available = !product.availability.is_empty() || product.featured;

                            html! {
                                <Link<Route> to={Route::Product { id: product.id.clone() }}>
                                    <Card hover={true} class="group cursor-pointer h-full">
                                        <div class="aspect-square bg-gray-200 relative overflow-hidden">
                                            <ProductImage
                                                product_images={product.images.clone()}
                                                alt={product.character_name.clone()}
                                                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                            />
                                            if !available {
                                                <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                    <Badge variant={BadgeVariant::Warning}>
                                                        {"Hết hàng"}
                                                    </Badge>
                                                </div>
                                            }
                                        </div>
                                        <CardBody>
                                            <div class="mb-2">
                                                <Badge variant={BadgeVariant::Primary} size={BadgeSize::Small}>
                                                    {product.franchise.clone()}
                                                </Badge>
                                            </div>
                                            <h3 class="font-semibold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors">
                                                {&product.character_name}
                                            </h3>
                                            <p class="text-primary-600 font-medium">
                                                {format!("Từ {} – 600.000 ₫", price_text)}
                                            </p>
                                        </CardBody>
                                    </Card>
                                </Link<Route>>
                            }
                        }) }
                    </Grid>

                    <div class="text-center mt-12">
                        <Link<Route> to={Route::Catalog}>
                            <Button variant={ButtonVariant::Primary} size={ButtonSize::Large}>
                                {"Xem Thêm Trang Phục"}
                            </Button>
                        </Link<Route>>
                    </div>
                }
            </Container>
        </Section>
    }
}

#[function_component(AboutSection)]
fn about_section() -> Html {
    let features = vec![
        ("Đa dạng", "Với hơn 200 trang phục của các nhân vật anime và manga. Bạn có thể thỏa thích hóa trang thành nhân vật bạn mong muốn dễ dàng.", IconType::Star),
        ("Dịch Vụ", "PUKA có đầy đủ đạo cụ và view mà bạn cần để thỏa sức trải nghiệm và biến hóa, tạo ra các tác phẩm của riêng bạn tại Studio của chúng tôi.", IconType::Camera),
        ("Tiện lợi", "Chỉ một vài thao tác là bạn đã hoàn tất việc thuê đồ nhanh chóng mà không phải cần phải đợi. Chúng tôi luôn sẵn sàng hỗ trợ khi bạn cần.", IconType::Clock),
    ];

    html! {
        <Section class="bg-white">
            <Container>
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        {"PUKA có gì?"}
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        {"Dành điều tuyệt vời nhất đến bạn. Sống theo bản thân mình mong muốn và cùng trở nên tuyệt vời hơn"}
                    </p>
                </div>

                <Grid cols={3} gap={GridGap::Large}>
                    { for features.iter().map(|(title, description, icon)| {
                        html! {
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-anime rounded-full flex items-center justify-center mx-auto mb-6">
                                    <Icon icon_type={*icon} class="h-8 w-8 text-white" />
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                                    {title}
                                </h3>
                                <p class="text-gray-600 leading-relaxed">
                                    {description}
                                </p>
                            </div>
                        }
                    }) }
                </Grid>
            </Container>
        </Section>
    }
}

#[function_component(CallToAction)]
fn call_to_action() -> Html {
    html! {
        <Section class="bg-gradient-anime text-white">
            <Container class="text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">
                    {"Hãy cảm thấy thoải mái khi liên hệ với chúng tôi"}
                </h2>
                <p class="text-xl mb-8 text-gray-100">
                    {"Sẵn sàng biến ước mơ cosplay của bạn thành hiện thực?"}
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link<Route> to={Route::Booking}>
                        <Button variant={ButtonVariant::Secondary} size={ButtonSize::Large} class="w-full sm:w-auto">
                            <Icon icon_type={IconType::Calendar} class="mr-2 h-5 w-5" />
                            {"Đặt Lịch Ngay"}
                        </Button>
                    </Link<Route>>
                    <Link<Route> to={Route::Contact}>
                        <Button variant={ButtonVariant::Outline} size={ButtonSize::Large} class="w-full sm:w-auto border-white text-white hover:bg-white hover:text-primary-600">
                            <Icon icon_type={IconType::MessageCircle} class="mr-2 h-5 w-5" />
                            {"Nhắn Tin Với Studio"}
                        </Button>
                    </Link<Route>>
                </div>
            </Container>
        </Section>
    }
}
