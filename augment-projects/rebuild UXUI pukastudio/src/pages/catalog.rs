use crate::components::{
    layout::{Container, Grid, GridGap, Section},
    ui::{
        Badge, BadgeSize, Badge<PERSON>, Button, ButtonSize, ButtonVariant, Card, CardBody,
        HashtagSection, Icon, IconType, LoadingSpinner, ProductImage,
    },
};
use crate::services::mock_data::load_products_from_json;
use crate::types::{Product, SortOption};
use crate::Route;
use yew::prelude::*;
use yew_router::prelude::*;

#[derive(Properties, PartialEq)]
pub struct CatalogPageProps {
    #[prop_or_default]
    pub category: Option<String>,
}

#[function_component(CatalogPage)]
pub fn catalog_page(props: &CatalogPageProps) -> Html {
    let products = use_state(|| Vec::<Product>::new());
    let loading = use_state(|| true);
    let error = use_state(|| None::<String>);
    let current_page = use_state(|| 1);
    let sort_option = use_state(|| SortOption::Default);
    let products_per_page = 12;

    // Load products from JSON
    {
        let products = products.clone();
        let loading = loading.clone();
        let error = error.clone();

        use_effect_with((), move |_| {
            wasm_bindgen_futures::spawn_local(async move {
                match load_products_from_json().await {
                    Ok(loaded_products) => {
                        products.set(loaded_products);
                        loading.set(false);
                    }
                    Err(e) => {
                        error.set(Some(e));
                        loading.set(false);
                    }
                }
            });
            || ()
        });
    }

    // Filter products by category if specified
    let filtered_products = {
        let products = (*products).clone();
        let category = props.category.clone();

        if let Some(cat) = category {
            match cat.as_str() {
                "rent" => {
                    // "rent" category shows all products since this is a rental service
                    products
                }
                "game" | "anime" | "vtuber" | "vocaloid" | "ero" | "other" => {
                    // Filter by category type (game, anime, vtuber, etc.)
                    products
                        .into_iter()
                        .filter(|p| p.category.as_str() == cat.to_lowercase())
                        .collect::<Vec<_>>()
                }
                _ => {
                    // Filter by specific franchise name (e.g., "Genshin Impact", "One Piece")
                    // First try to match by franchise name
                    let franchise_filtered: Vec<_> = products
                        .iter()
                        .filter(|p| {
                            p.franchise.to_lowercase().replace(" ", "-") == cat.to_lowercase() ||
                            p.franchise.to_lowercase() == cat.to_lowercase().replace("-", " ")
                        })
                        .cloned()
                        .collect();

                    if !franchise_filtered.is_empty() {
                        franchise_filtered
                    } else {
                        // If no franchise match, try category type as fallback
                        products
                            .into_iter()
                            .filter(|p| p.category.as_str() == cat.to_lowercase())
                            .collect::<Vec<_>>()
                    }
                }
            }
        } else {
            products
        }
    };

    // Sort products
    let sorted_products = {
        let mut products = filtered_products.clone();
        match *sort_option {
            SortOption::Default => {
                // Keep original order
            }
            SortOption::PriceLowToHigh => {
                products.sort_by(|a, b| {
                    let price_a = a.rental_options.first().map(|r| r.price).unwrap_or(0);
                    let price_b = b.rental_options.first().map(|r| r.price).unwrap_or(0);
                    price_a.cmp(&price_b)
                });
            }
            SortOption::PriceHighToLow => {
                products.sort_by(|a, b| {
                    let price_a = a.rental_options.first().map(|r| r.price).unwrap_or(0);
                    let price_b = b.rental_options.first().map(|r| r.price).unwrap_or(0);
                    price_b.cmp(&price_a)
                });
            }
            SortOption::Newest => {
                products.sort_by(|a, b| b.created_at.cmp(&a.created_at));
            }
            SortOption::Popular => {
                products.sort_by(|a, b| b.featured.cmp(&a.featured));
            }
        }
        products
    };

    // Pagination
    let total_products = sorted_products.len();
    let total_pages = (total_products + products_per_page - 1) / products_per_page;
    let start_index = (*current_page - 1) * products_per_page;
    let end_index = (start_index + products_per_page).min(total_products);
    let current_products = sorted_products[start_index..end_index].to_vec();

    let on_sort_change = {
        let sort_option = sort_option.clone();
        let current_page = current_page.clone();
        Callback::from(move |new_sort: SortOption| {
            sort_option.set(new_sort);
            current_page.set(1); // Reset to first page when sorting
        })
    };

    let on_page_change = {
        let current_page = current_page.clone();
        Callback::from(move |page: usize| {
            current_page.set(page);
        })
    };

    if *loading {
        return html! {
            <Section class="py-12">
                <Container>
                    <div class="text-center">
                        <LoadingSpinner size={32} />
                        <p class="text-gray-500 mt-4">{"Đang tải sản phẩm..."}</p>
                    </div>
                </Container>
            </Section>
        };
    }

    if let Some(err) = error.as_ref() {
        return html! {
            <Section class="py-12">
                <Container>
                    <div class="text-center">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                            <Icon icon_type={IconType::AlertTriangle} class="w-12 h-12 text-red-500 mx-auto mb-4" />
                            <h3 class="text-lg font-semibold text-red-800 mb-2">{"Không thể tải dữ liệu thực"}</h3>
                            <p class="text-red-600 mb-4">{"Hệ thống đang sử dụng dữ liệu thực từ website gốc nhưng không thể tải được."}</p>
                            <details class="text-left">
                                <summary class="cursor-pointer text-red-700 font-medium">{"Chi tiết lỗi"}</summary>
                                <pre class="mt-2 text-xs text-red-600 bg-red-100 p-2 rounded overflow-auto">
                                    {err}
                                </pre>
                            </details>
                            <Button
                                variant={ButtonVariant::Primary}
                                onclick={Callback::from(|_| {
                                    web_sys::window().unwrap().location().reload().unwrap();
                                })}
                                class="mt-4"
                            >
                                {"Thử lại"}
                            </Button>
                        </div>
                    </div>
                </Container>
            </Section>
        };
    }

    html! {
        <div class="min-h-screen bg-gray-50">
            <CatalogHeader
                category={props.category.clone()}
                total_products={total_products}
                sort_option={*sort_option}
                on_sort_change={on_sort_change}
                current_products={current_products.clone()}
            />

            <Section class="py-4">
                <Container>
                    if current_products.is_empty() {
                        <div class="text-center py-12">
                            <p class="text-gray-500 text-lg">{"Không tìm thấy sản phẩm nào."}</p>
                        </div>
                    } else {
                        <>
                            <ProductGrid products={current_products} />

                            if total_pages > 1 {
                                <Pagination
                                    current_page={*current_page}
                                    total_pages={total_pages}
                                    on_page_change={on_page_change}
                                />
                            }
                        </>
                    }
                </Container>
            </Section>
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct CatalogHeaderProps {
    pub category: Option<String>,
    pub total_products: usize,
    pub sort_option: SortOption,
    pub on_sort_change: Callback<SortOption>,
    pub current_products: Vec<Product>,
}

#[function_component(CatalogHeader)]
fn catalog_header(props: &CatalogHeaderProps) -> Html {
    let category_name = props
        .category
        .as_ref()
        .map(|c| match c.as_str() {
            "rent" => "Cho Thuê".to_string(),
            "game" => "Game".to_string(),
            "anime" => "Anime".to_string(),
            "vtuber" => "VTuber".to_string(),
            "vocaloid" => "Vocaloid".to_string(),
            "ero" => "Ero".to_string(),
            _ => {
                // Convert URL-friendly names back to display names
                c.replace("-", " ")
                    .split_whitespace()
                    .map(|word| {
                        let mut chars = word.chars();
                        match chars.next() {
                            None => String::new(),
                            Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
                        }
                    })
                    .collect::<Vec<_>>()
                    .join(" ")
            }
        })
        .unwrap_or_else(|| "Tất cả sản phẩm".to_string());

    let on_sort_select = {
        let on_sort_change = props.on_sort_change.clone();
        Callback::from(move |e: Event| {
            let target = e.target_dyn_into::<web_sys::HtmlSelectElement>().unwrap();
            let value = target.value();
            let sort_option = match value.as_str() {
                "default" => SortOption::Default,
                "price_low_high" => SortOption::PriceLowToHigh,
                "price_high_low" => SortOption::PriceHighToLow,
                "newest" => SortOption::Newest,
                "popular" => SortOption::Popular,
                _ => SortOption::Default,
            };
            on_sort_change.emit(sort_option);
        })
    };

    html! {
        <Section class="bg-white border-b py-4">
            <Container>
                <div class="py-2">
                    // Breadcrumb
                    <nav class="flex mb-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li class="inline-flex items-center">
                                <Link<Route> to={Route::Home}
                                    classes="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                    <Icon icon_type={IconType::Home} class="w-4 h-4 mr-2" />
                                    {"Trang Chủ"}
                                </Link<Route>>
                            </li>
                            <li>
                                <div class="flex items-center">
                                    <Icon icon_type={IconType::ChevronRight} class="w-4 h-4 text-gray-400 mx-1" />
                                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                                        {&category_name}
                                    </span>
                                </div>
                            </li>
                        </ol>
                    </nav>

                    // Header
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 mb-1">
                                {&category_name}
                            </h1>
                            <p class="text-gray-600">
                                {format!("Hiển thị 1–12 của {} kết quả", props.total_products)}
                            </p>
                        </div>

                        // Sort dropdown
                        <div class="mt-4 md:mt-0">
                            <select
                                class="block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                onchange={on_sort_select}
                                value={match props.sort_option {
                                    SortOption::Default => "default",
                                    SortOption::PriceLowToHigh => "price_low_high",
                                    SortOption::PriceHighToLow => "price_high_low",
                                    SortOption::Newest => "newest",
                                    SortOption::Popular => "popular",
                                }}
                            >
                                <option value="default">{"Thứ tự mặc định"}</option>
                                <option value="popular">{"Thứ tự theo mức độ phổ biến"}</option>
                                <option value="newest">{"Thứ tự theo mới nhất"}</option>
                                <option value="price_low_high">{"Thứ tự theo giá: thấp đến cao"}</option>
                                <option value="price_high_low">{"Thứ tự theo giá: cao đến thấp"}</option>
                            </select>
                        </div>
                    </div>

                    // Hashtag Section
                    <HashtagSection products={props.current_products.clone()} />
                </div>
            </Container>
        </Section>
    }
}

#[derive(Properties, PartialEq)]
pub struct ProductGridProps {
    pub products: Vec<Product>,
}

#[function_component(ProductGrid)]
fn product_grid(props: &ProductGridProps) -> Html {
    html! {
        <Grid gap={GridGap::Medium} class="grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {for props.products.iter().map(|product| {
                html! {
                    <ProductCard key={product.id.clone()} product={product.clone()} />
                }
            })}
        </Grid>
    }
}

#[derive(Properties, PartialEq)]
pub struct ProductCardProps {
    pub product: Product,
}

#[function_component(ProductCard)]
fn product_card(props: &ProductCardProps) -> Html {
    let product = &props.product;
    let min_price = product
        .rental_options
        .iter()
        .map(|r| r.price)
        .min()
        .unwrap_or(0);
    let max_price = product
        .rental_options
        .iter()
        .map(|r| r.price)
        .max()
        .unwrap_or(0);

    let price_text = if min_price == max_price {
        format!("{}.000 ₫", min_price / 1000)
    } else {
        format!("{}.000 ₫ – {}.000 ₫", min_price / 1000, max_price / 1000)
    };

    html! {
        <Link<Route> to={Route::Product { id: product.id.clone() }}>
            <Card class="group hover:shadow-lg transition-shadow duration-300 h-full cursor-pointer">
                <div class="relative overflow-hidden">
                    <ProductImage
                        product_images={product.images.clone()}
                        alt={product.name.clone()}
                        class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                        show_thumbnails={false}
                    />

                    // Category badge
                    <div class="absolute top-2 left-2">
                        <Badge variant={BadgeVariant::Primary} size={BadgeSize::Small}>
                            {product.franchise.clone()}
                        </Badge>
                    </div>

                    // Out of stock overlay (if needed)
                    // You can add availability logic here
                </div>

                <CardBody class="p-4">
                    <div class="mb-2">
                        <p class="text-sm text-gray-500 mb-1">{&product.franchise}</p>
                        <h3 class="font-semibold text-gray-900 line-clamp-2 group-hover:text-primary-600 transition-colors">
                            {&product.name}
                        </h3>
                    </div>

                    <div class="flex items-center mb-3">
                        // Star rating (placeholder)
                        <div class="flex items-center">
                            {for (0..5).map(|_| {
                                html! {
                                    <Icon icon_type={IconType::Star} class="w-4 h-4 text-gray-300" />
                                }
                            })}
                            <span class="text-sm text-gray-500 ml-1">{"Rated 0 out of 5"}</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg font-bold text-gray-900">{price_text}</p>
                            <p class="text-sm text-blue-600">
                                {"Xem chi tiết"}
                            </p>
                        </div>

                        <div class="text-sm text-primary-600 font-medium">
                            {"Chọn tùy chọn →"}
                        </div>
                    </div>

                    <p class="text-xs text-gray-500 mt-2">
                        {"Sản phẩm này có nhiều biến thể. Các tùy chọn có thể được chọn trên trang sản phẩm"}
                    </p>
                </CardBody>
            </Card>
        </Link<Route>>
    }
}

#[derive(Properties, PartialEq)]
pub struct PaginationProps {
    pub current_page: usize,
    pub total_pages: usize,
    pub on_page_change: Callback<usize>,
}

#[function_component(Pagination)]
fn pagination(props: &PaginationProps) -> Html {
    let current_page = props.current_page;
    let total_pages = props.total_pages;

    // Generate page numbers to show
    let mut pages_to_show = Vec::new();

    // Always show first page
    pages_to_show.push(1);

    // Show pages around current page
    let start = (current_page.saturating_sub(2)).max(2);
    let end = (current_page + 2).min(total_pages);

    if start > 2 {
        pages_to_show.push(0); // Represents "..."
    }

    for page in start..=end {
        if page != 1 && page != total_pages {
            pages_to_show.push(page);
        }
    }

    if end < total_pages - 1 {
        pages_to_show.push(0); // Represents "..."
    }

    // Always show last page if there's more than one page
    if total_pages > 1 {
        pages_to_show.push(total_pages);
    }

    html! {
        <nav class="flex justify-center mt-8" aria-label="Pagination">
            <ul class="flex items-center space-x-1">
                // Previous button
                <li>
                    <button
                        class={classes!(
                            "px-3", "py-2", "text-sm", "font-medium", "text-gray-500", "bg-white", "border", "border-gray-300", "rounded-l-lg", "hover:bg-gray-100", "hover:text-gray-700",
                            if current_page == 1 { "cursor-not-allowed opacity-50" } else { "" }
                        )}
                        disabled={current_page == 1}
                        onclick={
                            let on_page_change = props.on_page_change.clone();
                            Callback::from(move |_| {
                                if current_page > 1 {
                                    on_page_change.emit(current_page - 1);
                                }
                            })
                        }
                    >
                        <Icon icon_type={IconType::ChevronLeft} class="w-4 h-4" />
                    </button>
                </li>

                // Page numbers
                {for pages_to_show.iter().enumerate().map(|(i, &page)| {
                    if page == 0 {
                        // Ellipsis
                        html! {
                            <li key={format!("ellipsis-{}", i)}>
                                <span class="px-3 py-2 text-sm font-medium text-gray-500">{"…"}</span>
                            </li>
                        }
                    } else {
                        let is_current = page == current_page;
                        let on_page_change = props.on_page_change.clone();

                        html! {
                            <li key={page.to_string()}>
                                <button
                                    class={classes!(
                                        "px-3", "py-2", "text-sm", "font-medium", "border",
                                        if is_current {
                                            "text-blue-600 bg-blue-50 border-blue-300"
                                        } else {
                                            "text-gray-500 bg-white border-gray-300 hover:bg-gray-100 hover:text-gray-700"
                                        }
                                    )}
                                    onclick={Callback::from(move |_| {
                                        on_page_change.emit(page);
                                    })}
                                >
                                    {page}
                                </button>
                            </li>
                        }
                    }
                })}

                // Next button
                <li>
                    <button
                        class={classes!(
                            "px-3", "py-2", "text-sm", "font-medium", "text-gray-500", "bg-white", "border", "border-gray-300", "rounded-r-lg", "hover:bg-gray-100", "hover:text-gray-700",
                            if current_page == total_pages { "cursor-not-allowed opacity-50" } else { "" }
                        )}
                        disabled={current_page == total_pages}
                        onclick={
                            let on_page_change = props.on_page_change.clone();
                            Callback::from(move |_| {
                                if current_page < total_pages {
                                    on_page_change.emit(current_page + 1);
                                }
                            })
                        }
                    >
                        <Icon icon_type={IconType::ChevronRight} class="w-4 h-4" />
                    </button>
                </li>
            </ul>
        </nav>
    }
}
