use chrono::{DateTime, Utc};
use wasm_bindgen::JsCast;
use web_sys::window;

pub mod format;
pub mod theme;
pub mod validation;

// Re-exports removed to fix unused import warnings

/// Format price in Vietnamese Dong
pub fn format_price(price: u32) -> String {
    // Simple formatting without comma separator for now
    format!("{} ₫", price)
}

/// Format date for display
pub fn format_date(date: &DateTime<Utc>) -> String {
    date.format("%d/%m/%Y").to_string()
}

/// Format datetime for display
pub fn format_datetime(date: &DateTime<Utc>) -> String {
    date.format("%d/%m/%Y %H:%M").to_string()
}

/// Get current timestamp
pub fn now() -> DateTime<Utc> {
    Utc::now()
}

/// Scroll to top of page
pub fn scroll_to_top() {
    if let Some(window) = window() {
        let _ = window.scroll_to_with_x_and_y(0.0, 0.0);
    }
}

/// Scroll to element by id
pub fn scroll_to_element(id: &str) {
    if let Some(window) = window() {
        if let Some(document) = window.document() {
            if let Some(element) = document.get_element_by_id(id) {
                element.scroll_into_view();
            }
        }
    }
}

/// Generate a random ID
pub fn generate_id() -> String {
    uuid::Uuid::new_v4().to_string()
}

/// Debounce function for search inputs
pub struct Debouncer {
    timeout_id: Option<i32>,
}

impl Debouncer {
    pub fn new() -> Self {
        Self { timeout_id: None }
    }

    pub fn debounce<F>(&mut self, delay_ms: i32, callback: F)
    where
        F: Fn() + 'static,
    {
        // Clear existing timeout
        if let Some(id) = self.timeout_id {
            if let Some(window) = window() {
                window.clear_timeout_with_handle(id);
            }
        }

        // Set new timeout
        if let Some(window) = window() {
            let closure = wasm_bindgen::closure::Closure::wrap(Box::new(callback) as Box<dyn Fn()>);
            if let Ok(id) = window.set_timeout_with_callback_and_timeout_and_arguments_0(
                closure.as_ref().unchecked_ref(),
                delay_ms,
            ) {
                self.timeout_id = Some(id);
            }
            closure.forget();
        }
    }
}

/// Local storage utilities
pub mod storage {
    use gloo_storage::{LocalStorage, Storage};
    use serde::{Deserialize, Serialize};

    pub fn set<T>(key: &str, value: &T) -> Result<(), gloo_storage::errors::StorageError>
    where
        T: Serialize,
    {
        LocalStorage::set(key, value)
    }

    pub fn get<T>(key: &str) -> Result<T, gloo_storage::errors::StorageError>
    where
        T: for<'de> Deserialize<'de>,
    {
        LocalStorage::get(key)
    }

    pub fn remove(key: &str) {
        LocalStorage::delete(key);
    }

    pub fn clear() {
        LocalStorage::clear();
    }
}

/// URL utilities
pub mod url {
    use crate::types::SearchFilters;
    use web_sys::{window, Url, UrlSearchParams};

    pub fn get_query_param(key: &str) -> Option<String> {
        if let Some(window) = window() {
            if let Some(location) = window.location().href().ok() {
                if let Ok(url) = Url::new(&location) {
                    let params = url.search_params();
                    return params.get(key);
                }
            }
        }
        None
    }

    pub fn build_search_url(filters: &SearchFilters) -> String {
        let params = UrlSearchParams::new().unwrap();

        if let Some(query) = &filters.query {
            params.set("q", query);
        }

        if let Some(category) = &filters.category {
            params.set("category", category.as_str());
        }

        if let Some(franchise) = &filters.franchise {
            params.set("franchise", franchise);
        }

        if let Some(size) = &filters.size {
            params.set("size", &size.as_str());
        }

        if let Some(price_min) = filters.price_min {
            params.set("price_min", &price_min.to_string());
        }

        if let Some(price_max) = filters.price_max {
            params.set("price_max", &price_max.to_string());
        }

        params.set("sort", filters.sort_by.as_str());

        format!(
            "/catalog?{}",
            params.to_string().as_string().unwrap_or_default()
        )
    }
}

/// Image utilities
pub mod image {
    /// Get optimized image URL based on device pixel ratio and size
    pub fn get_optimized_url(base_url: &str, width: u32, height: u32) -> String {
        // In a real implementation, this would integrate with an image optimization service
        // For now, we'll just return the base URL
        format!("{}?w={}&h={}&fit=crop&auto=format", base_url, width, height)
    }

    /// Get placeholder image URL
    pub fn get_placeholder_url(width: u32, height: u32) -> String {
        format!(
            "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='{}' height='{}'%3E%3Crect width='100%25' height='100%25' fill='%23f3f4f6'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%239ca3af'%3ELoading...%3C/text%3E%3C/svg%3E",
            width, height
        )
    }
}

/// Animation utilities
pub mod animation {
    use web_sys::{window, Element};

    pub fn fade_in(element: &Element, duration_ms: u32) {
        use wasm_bindgen::JsCast;

        let element = element.clone();
        let start_time = js_sys::Date::now();

        // Create a recursive animation function
        fn animate_step(element: Element, start_time: f64, duration_ms: u32) {
            let elapsed = js_sys::Date::now() - start_time;
            let progress = (elapsed / duration_ms as f64).min(1.0);
            let opacity = progress;

            if let Some(html_element) = element.dyn_ref::<web_sys::HtmlElement>() {
                let _ = html_element
                    .style()
                    .set_property("opacity", &opacity.to_string());
            }

            if progress < 1.0 {
                if let Some(window) = window() {
                    let element_clone = element.clone();
                    let closure = wasm_bindgen::closure::Closure::wrap(Box::new(move || {
                        animate_step(element_clone.clone(), start_time, duration_ms);
                    })
                        as Box<dyn Fn()>);
                    let _ = window.request_animation_frame(closure.as_ref().unchecked_ref());
                    closure.forget();
                }
            }
        }

        animate_step(element, start_time, duration_ms);
    }
}
