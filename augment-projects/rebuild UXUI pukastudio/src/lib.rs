use components::demo::Demo;
use utils::theme::ThemeProvider;
use yew::prelude::*;
use yew_router::prelude::*;

mod components;
mod pages;
mod services;
mod types;
mod utils;

use components::layout::Layout;
use pages::{
    booking::BookingPage, catalog::CatalogPage, contact::ContactPage, home::HomePage,
    not_found::NotFoundPage, product::ProductPage, search::SearchPage,
};

#[derive(Clone, Routable, PartialEq)]
pub enum Route {
    #[at("/")]
    Home,
    #[at("/catalog")]
    Catalog,
    #[at("/catalog/:category")]
    CatalogCategory { category: String },
    #[at("/product-category/:category")]
    ProductCategory { category: String },
    #[at("/product/:id")]
    Product { id: String },
    #[at("/search/:query")]
    Search { query: String },
    #[at("/booking")]
    Booking,
    #[at("/booking/:product_id")]
    BookingProduct { product_id: String },
    #[at("/contact")]
    Contact,
    #[at("/demo")]
    Demo,
    #[not_found]
    #[at("/404")]
    NotFound,
}

fn switch(routes: Route) -> Html {
    match routes {
        Route::Home => html! { <HomePage /> },
        Route::Catalog => html! { <CatalogPage /> },
        Route::CatalogCategory { category } => html! { <CatalogPage category={Some(category)} /> },
        Route::ProductCategory { category } => html! { <CatalogPage category={Some(category)} /> },
        Route::Product { id } => html! { <ProductPage {id} /> },
        Route::Search { query } => html! { <SearchPage {query} /> },
        Route::Booking => html! { <BookingPage /> },
        Route::BookingProduct { product_id } => html! { <BookingPage {product_id} /> },
        Route::Contact => html! { <ContactPage /> },
        Route::Demo => html! { <Demo /> },
        Route::NotFound => html! { <NotFoundPage /> },
    }
}

#[function_component(App)]
pub fn app() -> Html {
    html! {
        <ThemeProvider>
            <BrowserRouter>
                <Layout>
                    <Switch<Route> render={switch} />
                </Layout>
            </BrowserRouter>
        </ThemeProvider>
    }
}

#[wasm_bindgen::prelude::wasm_bindgen(start)]
pub fn run_app() {
    wasm_logger::init(wasm_logger::Config::default());
    yew::Renderer::<App>::new().render();
}
