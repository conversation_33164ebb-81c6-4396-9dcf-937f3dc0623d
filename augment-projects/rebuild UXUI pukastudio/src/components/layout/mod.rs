use crate::Route;
use yew::prelude::*;
use yew_router::prelude::*;

mod footer;
mod header;
mod navigation;

pub use footer::*;
pub use header::*;
pub use navigation::*;

#[derive(Properties, PartialEq)]
pub struct LayoutProps {
    pub children: Children,
}

#[function_component(Layout)]
pub fn layout(props: &LayoutProps) -> Html {
    html! {
        <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col transition-colors duration-200">
            <Header />
            <main class="flex-1">
                { for props.children.iter() }
            </main>
            <Footer />
        </div>
    }
}

#[function_component(Container)]
pub fn container(props: &ContainerProps) -> Html {
    let class = classes!("container-custom", props.class.clone());

    html! {
        <div class={class}>
            { for props.children.iter() }
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct ContainerProps {
    pub children: Children,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(Section)]
pub fn section(props: &SectionProps) -> Html {
    // Only add default padding if no custom padding classes are provided
    let has_custom_padding = props.class.to_string().contains("py-")
        || props.class.to_string().contains("pt-")
        || props.class.to_string().contains("pb-");

    let class = if has_custom_padding {
        classes!(props.class.clone())
    } else {
        classes!("py-12", "lg:py-16", props.class.clone())
    };

    html! {
        <section class={class}>
            <Container class={props.container_class.clone()}>
                { for props.children.iter() }
            </Container>
        </section>
    }
}

#[derive(Properties, PartialEq)]
pub struct SectionProps {
    pub children: Children,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or_default]
    pub container_class: Classes,
}

#[function_component(Grid)]
pub fn grid(props: &GridProps) -> Html {
    let class = classes!(
        "grid",
        match props.cols {
            1 => "grid-cols-1",
            2 => "grid-cols-1 md:grid-cols-2",
            3 => "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
            4 => "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
            5 => "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",
            6 => "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6",
            _ => "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
        },
        match props.gap {
            GridGap::Small => "gap-4",
            GridGap::Medium => "gap-6",
            GridGap::Large => "gap-8",
        },
        props.class.clone()
    );

    html! {
        <div class={class}>
            { for props.children.iter() }
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct GridProps {
    pub children: Children,
    #[prop_or(3)]
    pub cols: u8,
    #[prop_or(GridGap::Medium)]
    pub gap: GridGap,
    #[prop_or_default]
    pub class: Classes,
}

#[derive(Clone, PartialEq)]
pub enum GridGap {
    Small,
    Medium,
    Large,
}

#[function_component(Flex)]
pub fn flex(props: &FlexProps) -> Html {
    let class = classes!(
        "flex",
        match props.direction {
            FlexDirection::Row => "flex-row",
            FlexDirection::Column => "flex-col",
            FlexDirection::RowReverse => "flex-row-reverse",
            FlexDirection::ColumnReverse => "flex-col-reverse",
        },
        match props.justify {
            FlexJustify::Start => "justify-start",
            FlexJustify::End => "justify-end",
            FlexJustify::Center => "justify-center",
            FlexJustify::Between => "justify-between",
            FlexJustify::Around => "justify-around",
            FlexJustify::Evenly => "justify-evenly",
        },
        match props.align {
            FlexAlign::Start => "items-start",
            FlexAlign::End => "items-end",
            FlexAlign::Center => "items-center",
            FlexAlign::Baseline => "items-baseline",
            FlexAlign::Stretch => "items-stretch",
        },
        match props.wrap {
            FlexWrap::NoWrap => "flex-nowrap",
            FlexWrap::Wrap => "flex-wrap",
            FlexWrap::WrapReverse => "flex-wrap-reverse",
        },
        match props.gap {
            FlexGap::None => "",
            FlexGap::Small => "gap-2",
            FlexGap::Medium => "gap-4",
            FlexGap::Large => "gap-6",
        },
        props.class.clone()
    );

    html! {
        <div class={class}>
            { for props.children.iter() }
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct FlexProps {
    pub children: Children,
    #[prop_or(FlexDirection::Row)]
    pub direction: FlexDirection,
    #[prop_or(FlexJustify::Start)]
    pub justify: FlexJustify,
    #[prop_or(FlexAlign::Start)]
    pub align: FlexAlign,
    #[prop_or(FlexWrap::NoWrap)]
    pub wrap: FlexWrap,
    #[prop_or(FlexGap::None)]
    pub gap: FlexGap,
    #[prop_or_default]
    pub class: Classes,
}

#[derive(Clone, PartialEq)]
pub enum FlexDirection {
    Row,
    Column,
    RowReverse,
    ColumnReverse,
}

#[derive(Clone, PartialEq)]
pub enum FlexJustify {
    Start,
    End,
    Center,
    Between,
    Around,
    Evenly,
}

#[derive(Clone, PartialEq)]
pub enum FlexAlign {
    Start,
    End,
    Center,
    Baseline,
    Stretch,
}

#[derive(Clone, PartialEq)]
pub enum FlexWrap {
    NoWrap,
    Wrap,
    WrapReverse,
}

#[derive(Clone, PartialEq)]
pub enum FlexGap {
    None,
    Small,
    Medium,
    Large,
}
