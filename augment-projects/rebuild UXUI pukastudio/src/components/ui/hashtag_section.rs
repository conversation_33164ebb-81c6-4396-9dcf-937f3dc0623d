use crate::types::Product;
use crate::Route;
use yew::prelude::*;
use yew_router::prelude::*;
use std::collections::HashSet;

#[derive(Clone, PartialEq)]
pub struct HashtagData {
    pub display_text: String,
    pub hashtag_type: HashtagType,
    pub original_value: String,
}

#[derive(Clone, PartialEq)]
pub enum HashtagType {
    Franchise,
    Category,
    Character,
}

#[derive(Properties, PartialEq)]
pub struct HashtagSectionProps {
    pub products: Vec<Product>,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(HashtagSection)]
pub fn hashtag_section(props: &HashtagSectionProps) -> Html {
    let hashtags = generate_hashtag_data(&props.products);
    let navigator = use_navigator().unwrap();

    if hashtags.is_empty() {
        return html! {};
    }

    html! {
        <div class={classes!("mt-4", "mb-2", props.class.clone())}>
            <div class="flex flex-wrap gap-2">
                {for hashtags.into_iter().enumerate().map(|(index, hashtag_data)| {
                    let color_class = get_hashtag_color_class(index);
                    let hashtag_data_clone = hashtag_data.clone();
                    let navigator_clone = navigator.clone();

                    let onclick = Callback::from(move |_: MouseEvent| {
                        handle_hashtag_click(&hashtag_data_clone, &navigator_clone);
                    });

                    html! {
                        <span
                            key={hashtag_data.display_text.clone()}
                            onclick={onclick}
                            class={classes!(
                                "inline-flex",
                                "items-center",
                                "px-3",
                                "py-1",
                                "rounded-full",
                                "text-sm",
                                "font-medium",
                                "text-white",
                                "transition-all",
                                "duration-200",
                                "hover:scale-105",
                                "hover:shadow-lg",
                                "cursor-pointer",
                                "select-none",
                                "active:scale-95",
                                color_class
                            )}
                            title={format!("Click to filter by {}", hashtag_data.display_text)}
                        >
                            <span class="mr-1">{"#"}</span>
                            {&hashtag_data.display_text}
                        </span>
                    }
                })}
            </div>
        </div>
    }
}

fn handle_hashtag_click(hashtag_data: &HashtagData, navigator: &Navigator) {
    match &hashtag_data.hashtag_type {
        HashtagType::Franchise => {
            // Navigate to franchise-specific catalog page
            let franchise_slug = hashtag_data.original_value.to_lowercase().replace(" ", "-");
            let route = Route::CatalogCategory { category: franchise_slug };
            navigator.push(&route);
        }
        HashtagType::Category => {
            // Navigate to category-specific catalog page
            let category_slug = hashtag_data.original_value.to_lowercase();
            let route = Route::CatalogCategory { category: category_slug };
            navigator.push(&route);
        }
        HashtagType::Character => {
            // For character hashtags, we could implement a search or filter
            // For now, let's navigate to a general catalog and let the user see all products
            // In a future enhancement, we could add character-specific filtering
            let route = Route::Catalog;
            navigator.push(&route);

            // Log for debugging - in a real app, you might want to implement character filtering
            web_sys::console::log_1(&format!("Character hashtag clicked: {}", hashtag_data.original_value).into());
        }
    }
}

fn generate_hashtag_data(products: &[Product]) -> Vec<HashtagData> {
    let mut hashtags = Vec::new();
    let mut seen = HashSet::new();

    // Collect unique franchises (limit to top 4 most common)
    let mut franchise_counts: std::collections::HashMap<String, usize> = std::collections::HashMap::new();
    for product in products {
        *franchise_counts.entry(product.franchise.clone()).or_insert(0) += 1;
    }

    let mut sorted_franchises: Vec<_> = franchise_counts.into_iter().collect();
    sorted_franchises.sort_by(|a, b| b.1.cmp(&a.1));

    for (franchise, _) in sorted_franchises.into_iter().take(4) {
        let display_text = format_hashtag(&franchise);
        if seen.insert(display_text.clone()) {
            hashtags.push(HashtagData {
                display_text,
                hashtag_type: HashtagType::Franchise,
                original_value: franchise,
            });
        }
    }

    // Add category types (limit to unique categories present)
    let mut categories = HashSet::new();
    for product in products {
        categories.insert(product.category.as_str());
    }

    for category in categories {
        let display_text = match category {
            "game" => "Game".to_string(),
            "anime" => "Anime".to_string(),
            "vtuber" => "VTuber".to_string(),
            "vocaloid" => "Vocaloid".to_string(),
            "ero" => "Ero".to_string(),
            _ => category.to_string(),
        };

        if seen.insert(display_text.clone()) && hashtags.len() < 8 {
            hashtags.push(HashtagData {
                display_text,
                hashtag_type: HashtagType::Category,
                original_value: category.to_string(),
            });
        }
    }

    // Add popular character names (limit to top 3)
    let mut character_counts: std::collections::HashMap<String, usize> = std::collections::HashMap::new();
    for product in products {
        *character_counts.entry(product.character_name.clone()).or_insert(0) += 1;
    }

    let mut sorted_characters: Vec<_> = character_counts.into_iter().collect();
    sorted_characters.sort_by(|a, b| b.1.cmp(&a.1));

    for (character, _) in sorted_characters.into_iter().take(3) {
        let display_text = format_hashtag(&character);
        if seen.insert(display_text.clone()) && hashtags.len() < 8 {
            hashtags.push(HashtagData {
                display_text,
                hashtag_type: HashtagType::Character,
                original_value: character,
            });
        }
    }

    hashtags
}

fn format_hashtag(text: &str) -> String {
    // Remove special characters and spaces, capitalize words
    text.chars()
        .filter(|c| c.is_alphanumeric() || c.is_whitespace())
        .collect::<String>()
        .split_whitespace()
        .map(|word| {
            let mut chars = word.chars();
            match chars.next() {
                None => String::new(),
                Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
            }
        })
        .collect::<Vec<_>>()
        .join("")
}

fn get_hashtag_color_class(index: usize) -> &'static str {
    match index % 8 {
        0 => "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700",
        1 => "bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700",
        2 => "bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700",
        3 => "bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700",
        4 => "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700",
        5 => "bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700",
        6 => "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700",
        7 => "bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700",
        _ => "bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700",
    }
}
