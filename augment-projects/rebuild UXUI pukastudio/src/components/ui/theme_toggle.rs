use crate::components::ui::{Icon, IconType};
use crate::utils::theme::{Theme, ThemeContext};
use yew::prelude::*;

/// Theme toggle button component
#[derive(Properties, PartialEq)]
pub struct ThemeToggleProps {
    #[prop_or_default]
    pub class: Classes,
    #[prop_or(false)]
    pub show_label: bool,
    #[prop_or(ThemeToggleVariant::Button)]
    pub variant: ThemeToggleVariant,
}

#[derive(Clone, PartialEq)]
pub enum ThemeToggleVariant {
    Button,
    Switch,
}

#[function_component(ThemeToggle)]
pub fn theme_toggle(props: &ThemeToggleProps) -> Html {
    let theme_context_opt = use_context::<ThemeContext>();

    if let Some(theme_context) = theme_context_opt {
        let current_theme = theme_context.theme;
        let set_theme = theme_context.set_theme;

        match props.variant {
            ThemeToggleVariant::Button => {
                render_button_toggle(props, current_theme, set_theme)
            }
            ThemeToggleVariant::Switch => {
                render_switch_toggle(props, current_theme, set_theme)
            }
        }
    } else {
        // Fallback if no theme context is available
        html! {
            <div class="text-red-500 text-sm">{"Theme context not available"}</div>
        }
    }
}

fn render_button_toggle(
    props: &ThemeToggleProps,
    current_theme: Theme,
    set_theme: Callback<Theme>,
) -> Html {
    let effective_theme = current_theme.effective_theme();
    
    let onclick = Callback::from(move |_| {
        let new_theme = match effective_theme {
            Theme::Light => Theme::Dark,
            Theme::Dark => Theme::Light,
            Theme::System => Theme::Light, // Shouldn't happen, but fallback
        };
        set_theme.emit(new_theme);
    });

    let (icon, label) = match effective_theme {
        Theme::Light => (IconType::Moon, "Dark Mode"),
        Theme::Dark => (IconType::Sun, "Light Mode"),
        Theme::System => (IconType::Sun, "Light Mode"), // Fallback
    };

    let class = classes!(
        "p-2",
        "text-gray-500",
        "hover:text-primary-600",
        "dark:text-gray-400",
        "dark:hover:text-primary-400",
        "transition-colors",
        "duration-200",
        "rounded-lg",
        "hover:bg-gray-100",
        "dark:hover:bg-gray-800",
        "focus:outline-none",
        "focus:ring-2",
        "focus:ring-primary-500",
        "focus:ring-offset-2",
        "dark:focus:ring-offset-gray-900",
        props.class.clone()
    );

    html! {
        <button
            {onclick}
            {class}
            aria-label={format!("Switch to {}", label)}
            title={format!("Switch to {}", label)}
        >
            <div class="flex items-center space-x-2">
                <Icon icon_type={icon} class="h-5 w-5" />
                if props.show_label {
                    <span class="text-sm font-medium">{label}</span>
                }
            </div>
        </button>
    }
}

fn render_switch_toggle(
    props: &ThemeToggleProps,
    current_theme: Theme,
    set_theme: Callback<Theme>,
) -> Html {
    let effective_theme = current_theme.effective_theme();
    let is_dark = effective_theme == Theme::Dark;
    
    let onclick = Callback::from(move |_| {
        let new_theme = if is_dark { Theme::Light } else { Theme::Dark };
        set_theme.emit(new_theme);
    });

    let class = classes!(
        "relative",
        "inline-flex",
        "h-6",
        "w-11",
        "items-center",
        "rounded-full",
        "transition-colors",
        "duration-200",
        "focus:outline-none",
        "focus:ring-2",
        "focus:ring-primary-500",
        "focus:ring-offset-2",
        "dark:focus:ring-offset-gray-900",
        if is_dark { "bg-primary-600" } else { "bg-gray-200" },
        props.class.clone()
    );

    let toggle_class = classes!(
        "inline-block",
        "h-4",
        "w-4",
        "transform",
        "rounded-full",
        "bg-white",
        "transition-transform",
        "duration-200",
        "shadow-lg",
        if is_dark { "translate-x-6" } else { "translate-x-1" }
    );

    html! {
        <div class="flex items-center space-x-3">
            if props.show_label {
                <Icon icon_type={IconType::Sun} class="h-4 w-4 text-gray-500 dark:text-gray-400" />
            }
            <button
                {onclick}
                {class}
                role="switch"
                aria-checked={is_dark.to_string()}
                aria-label="Toggle dark mode"
            >
                <span class={toggle_class} />
            </button>
            if props.show_label {
                <Icon icon_type={IconType::Moon} class="h-4 w-4 text-gray-500 dark:text-gray-400" />
            }
        </div>
    }
}