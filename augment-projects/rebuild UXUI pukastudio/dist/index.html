<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Puka Studio - Cosplay Costume Rental</title>
    <meta name="description" content="Puka Studio - Premium cosplay costume rental service specializing in anime and game character costumes in Ho Chi Minh City, Vietnam." />
    <meta name="keywords" content="cosplay, costume rental, anime, manga, games, Vietnam, Ho Chi Minh City" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://pukastudio.shop/" />
    <meta property="og:title" content="Puka Studio - Cosplay Costume Rental" />
    <meta property="og:description" content="Premium cosplay costume rental service specializing in anime and game character costumes." />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://pukastudio.shop/" />
    <meta property="twitter:title" content="Puka Studio - Cosplay Costume Rental" />
    <meta property="twitter:description" content="Premium cosplay costume rental service specializing in anime and game character costumes." />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/svg+xml" href="/images/logo.svg" />

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/tailwind-fb7d09f5c56756b4.css" integrity="sha384-N3TI89mu1NrLmm/mHWl7A2fRl6QqLPLPSsI7To/pgjsGbJy+GPLvh+RA97bYE293"/>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/main-6a12d7fa4a3fb207.css" integrity="sha384-ag+8kUfBzF+crG3OHxqFx/wMClrC3aPwjVNLyIkvwc0X+kuleSONFevkFXjGpf1l"/>

    <!-- Copy static assets -->
    
    

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
<link rel="modulepreload" href="/puka-studio-aac27d29d18ed10e.js" crossorigin="anonymous" integrity="sha384-06R8+Roz8KS+mZ4UI4JR9OfK2shLSjdfSOHcq8QYgxYE8VA75AnHJyAmHHjm798R"><link rel="preload" href="/puka-studio-aac27d29d18ed10e_bg.wasm" crossorigin="anonymous" integrity="sha384-Qd9+m7WMheU/w7AzATHptXu9EJCgElB4rDjP02VhxD138SMDSZ0YhYQoOWZ3xTQT" as="fetch" type="application/wasm"></head>
<body class="font-sans antialiased bg-gray-50">
    <div id="app"></div>

    <!-- Loading indicator -->
    <div id="loading" class="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading Puka Studio...</p>
        </div>
    </div>

    <script>
        // Hide loading indicator when app loads
        window.addEventListener('load', function() {
            const loading = document.getElementById('loading');
            if (loading) {
                loading.style.display = 'none';
            }
        });
    </script>

<script type="module">
import init, * as bindings from '/puka-studio-aac27d29d18ed10e.js';
const wasm = await init({ module_or_path: '/puka-studio-aac27d29d18ed10e_bg.wasm' });


window.wasmBindings = bindings;


dispatchEvent(new CustomEvent("TrunkApplicationStarted", {detail: {wasm}}));

</script><script>"use strict";

(function () {

    const address = '{{__TRUNK_ADDRESS__}}';
    const base = '{{__TRUNK_WS_BASE__}}';
    let protocol = '';
    protocol =
        protocol
            ? protocol
            : window.location.protocol === 'https:'
                ? 'wss'
                : 'ws';
    const url = protocol + '://' + address + base + '.well-known/trunk/ws';

    class Overlay {
        constructor() {
            // create an overlay
            this._overlay = document.createElement("div");
            const style = this._overlay.style;
            style.height = "100vh";
            style.width = "100vw";
            style.position = "fixed";
            style.top = "0";
            style.left = "0";
            style.backgroundColor = "rgba(222, 222, 222, 0.5)";
            style.fontFamily = "sans-serif";
            // not sure that's the right approach
            style.zIndex = "1000000";
            style.backdropFilter = "blur(1rem)";

            const container = document.createElement("div");
            // center it
            container.style.position = "absolute";
            container.style.top = "30%";
            container.style.left = "15%";
            container.style.maxWidth = "85%";

            this._title = document.createElement("div");
            this._title.innerText = "Build failure";
            this._title.style.paddingBottom = "2rem";
            this._title.style.fontSize = "2.5rem";

            this._message = document.createElement("div");
            this._message.style.whiteSpace = "pre-wrap";

            const icon= document.createElement("div");
            icon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="#dc3545" viewBox="0 0 16 16"><path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>';
            this._title.prepend(icon);

            container.append(this._title, this._message);
            this._overlay.append(container);

            this._inject();
            window.setInterval(() => {
                this._inject();
            }, 250);
        }

        set reason(reason) {
            this._message.textContent = reason;
        }

        _inject() {
            if (!this._overlay.isConnected) {
                // prepend it
                document.body?.prepend(this._overlay);
            }
        }

    }

    class Client {
        constructor(url) {
            this.url = url;
            this.poll_interval = 5000;
            this._overlay = null;
        }

        start() {
            const ws = new WebSocket(this.url);
            ws.onmessage = (ev) => {
                const msg = JSON.parse(ev.data);
                switch (msg.type) {
                    case "reload":
                        this.reload();
                        break;
                    case "buildFailure":
                        this.buildFailure(msg.data)
                        break;
                }
            };
            ws.onclose = () => this.onclose();
        }

        onclose() {
            window.setTimeout(
                () => {
                    // when we successfully reconnect, we'll force a
                    // reload (since we presumably lost connection to
                    // trunk due to it being killed, so it will have
                    // rebuilt on restart)
                    const ws = new WebSocket(this.url);
                    ws.onopen = () => window.location.reload();
                    ws.onclose = () => this.onclose();
                },
                this.poll_interval);
        }

        reload() {
            window.location.reload();
        }

        buildFailure({reason}) {
            // also log the console
            console.error("Build failed:", reason);

            console.debug("Overlay", this._overlay);

            if (!this._overlay) {
                this._overlay = new Overlay();
            }
            this._overlay.reason = reason;
        }
    }

    new Client(url).start();

})()
</script></body>
</html>
