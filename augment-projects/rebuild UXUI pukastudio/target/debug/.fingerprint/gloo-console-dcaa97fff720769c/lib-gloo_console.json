{"rustc": 5357548097637079788, "features": "[]", "declared_features": "[]", "target": 7192765726142894394, "profile": 8276155916380437441, "path": 17885419283812073703, "deps": [[5921074888975346911, "gloo_utils", false, 12362307562460729272], [6946689283190175495, "wasm_bindgen", false, 11288251756503893962], [8264480821543757363, "web_sys", false, 16454474965290173428], [9003359908906038687, "js_sys", false, 9689573191809966200], [9689903380558560274, "serde", false, 12569599343696240165]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/gloo-console-dcaa97fff720769c/dep-lib-gloo_console", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}