{"rustc": 5357548097637079788, "features": "[\"default\", \"futures\"]", "declared_features": "[\"default\", \"futures\"]", "target": 11217130877511374870, "profile": 8276155916380437441, "path": 16821689357194590170, "deps": [[65234016722529558, "bincode", false, 17924780526878648645], [2706460456408817945, "futures", false, 6670596392998705017], [5921074888975346911, "gloo_utils", false, 12362307562460729272], [6946689283190175495, "wasm_bindgen", false, 11288251756503893962], [8008191657135824715, "thiserror", false, 6600541754322386924], [8264480821543757363, "web_sys", false, 16454474965290173428], [9003359908906038687, "js_sys", false, 9689573191809966200], [9689903380558560274, "serde", false, 12569599343696240165], [12919484467758300183, "gloo_worker_macros", false, 14359168264920288329], [14997362514532562728, "pinned", false, 8924003009661006080], [15917073803248137067, "wasm_bindgen_futures", false, 14552354603639856164]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/gloo-worker-a86efffa06857dbb/dep-lib-gloo_worker", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}