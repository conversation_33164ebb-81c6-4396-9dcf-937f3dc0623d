{"rustc": 5357548097637079788, "features": "[\"default\", \"eventsource\", \"futures-channel\", \"futures-core\", \"futures-sink\", \"http\", \"json\", \"pin-project\", \"serde\", \"serde_json\", \"websocket\"]", "declared_features": "[\"default\", \"eventsource\", \"futures-channel\", \"futures-core\", \"futures-sink\", \"http\", \"json\", \"pin-project\", \"serde\", \"serde_json\", \"websocket\"]", "target": 7289951416308014359, "profile": 8276155916380437441, "path": 2430389916362185178, "deps": [[1811549171721445101, "futures_channel", false, 1025021162279805592], [4405182208873388884, "http", false, 14914950914919961458], [5921074888975346911, "gloo_utils", false, 1093497137391846858], [6264115378959545688, "pin_project", false, 17701908393307485084], [6946689283190175495, "wasm_bindgen", false, 11288251756503893962], [7013762810557009322, "futures_sink", false, 14398049346899432638], [7620660491849607393, "futures_core", false, 16218876647993608232], [8008191657135824715, "thiserror", false, 6600541754322386924], [8264480821543757363, "web_sys", false, 4387648670974931869], [9003359908906038687, "js_sys", false, 9689573191809966200], [9689903380558560274, "serde", false, 12569599343696240165], [15367738274754116744, "serde_json", false, 5503372487113712472], [15917073803248137067, "wasm_bindgen_futures", false, 14552354603639856164]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/gloo-net-30cb71f4cce0abe8/dep-lib-gloo_net", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}