{"rustc": 5357548097637079788, "features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "declared_features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "target": 17962624438068670222, "profile": 8276155916380437441, "path": 10780843913085627624, "deps": [[4106074096146763267, "gloo_events", false, 7122703628763223030], [5921074888975346911, "gloo_utils", false, 12362307562460729272], [6946689283190175495, "wasm_bindgen", false, 11288251756503893962], [8008191657135824715, "thiserror", false, 6600541754322386924], [8264480821543757363, "web_sys", false, 16454474965290173428], [9689903380558560274, "serde", false, 12569599343696240165], [11261232116272131900, "serde_wasm_bindgen", false, 1280240120321141866], [16542808166767769916, "serde_urlencoded", false, 18256483922141599952]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/gloo-history-3209272fd9649d21/dep-lib-gloo_history", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}