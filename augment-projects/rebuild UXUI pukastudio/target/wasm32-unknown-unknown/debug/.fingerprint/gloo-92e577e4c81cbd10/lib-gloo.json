{"rustc": 5357548097637079788, "features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "declared_features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"futures\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "target": 17023954276131510380, "profile": 15657897354478470176, "path": 5745164446932299719, "deps": [[5028561147853581999, "gloo_timers", false, 9374396242165073800], [9329111706356929223, "gloo_file", false, 9804338261637613913], [9979345710825402490, "gloo_dialogs", false, 16864302958301475509], [10340280370311140941, "gloo_worker", false, 4820030358937523931], [12069273292196623910, "gloo_events", false, 9402876593659975256], [12469029679378729817, "gloo_console", false, 14349607533673249132], [13787904963389927206, "gloo_storage", false, 29117481643841392], [15119418339232312804, "gloo_history", false, 12019268542303638610], [17073337587916847516, "gloo_render", false, 11213767993895735117], [17548459073812404046, "gloo_net", false, 6995944963709780095], [18122101786498147437, "gloo_utils", false, 13631161158373584015]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-92e577e4c81cbd10/dep-lib-gloo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}