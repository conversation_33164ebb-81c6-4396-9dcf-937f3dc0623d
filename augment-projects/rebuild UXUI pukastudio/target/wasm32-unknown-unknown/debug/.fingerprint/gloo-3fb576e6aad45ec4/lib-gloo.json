{"rustc": 5357548097637079788, "features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "declared_features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"futures\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "target": 17023954276131510380, "profile": 15657897354478470176, "path": 5745164446932299719, "deps": [[5028561147853581999, "gloo_timers", false, 9374396242165073800], [9329111706356929223, "gloo_file", false, 367647303938504089], [9979345710825402490, "gloo_dialogs", false, 2148192916458451577], [10340280370311140941, "gloo_worker", false, 1097778392686066791], [12069273292196623910, "gloo_events", false, 13682637081122952523], [12469029679378729817, "gloo_console", false, 7960826128530150693], [13787904963389927206, "gloo_storage", false, 1248534064973459927], [15119418339232312804, "gloo_history", false, 17320762030656905927], [17073337587916847516, "gloo_render", false, 16932357948334230715], [17548459073812404046, "gloo_net", false, 13589858440311022756], [18122101786498147437, "gloo_utils", false, 3478830328554578998]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-3fb576e6aad45ec4/dep-lib-gloo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}