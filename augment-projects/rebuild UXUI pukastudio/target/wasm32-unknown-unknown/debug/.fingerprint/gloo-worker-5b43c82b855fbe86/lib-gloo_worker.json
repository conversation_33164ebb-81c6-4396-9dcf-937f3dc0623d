{"rustc": 5357548097637079788, "features": "[\"default\"]", "declared_features": "[\"default\", \"futures\"]", "target": 15706070843846846069, "profile": 15657897354478470176, "path": 2376002852515578073, "deps": [[65234016722529558, "bincode", false, 11302470809687920160], [6946689283190175495, "wasm_bindgen", false, 8015492438657511113], [8264480821543757363, "web_sys", false, 14485297427298658205], [9003359908906038687, "js_sys", false, 16701719607648578680], [9689903380558560274, "serde", false, 9556902677958282247], [12469029679378729817, "gloo_console", false, 7960826128530150693], [13843926109129797205, "anymap2", false, 12851798544989376966], [15917073803248137067, "wasm_bindgen_futures", false, 17074736512650298747], [18122101786498147437, "gloo_utils", false, 3478830328554578998]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-worker-5b43c82b855fbe86/dep-lib-gloo_worker", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}