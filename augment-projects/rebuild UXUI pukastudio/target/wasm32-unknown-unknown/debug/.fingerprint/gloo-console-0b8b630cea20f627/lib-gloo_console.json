{"rustc": 5357548097637079788, "features": "[]", "declared_features": "[]", "target": 7192765726142894394, "profile": 15657897354478470176, "path": 17885419283812073703, "deps": [[5921074888975346911, "gloo_utils", false, 11478267699244409448], [6946689283190175495, "wasm_bindgen", false, 8015492438657511113], [8264480821543757363, "web_sys", false, 7600967520588414932], [9003359908906038687, "js_sys", false, 16701719607648578680], [9689903380558560274, "serde", false, 9556902677958282247]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-console-0b8b630cea20f627/dep-lib-gloo_console", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}