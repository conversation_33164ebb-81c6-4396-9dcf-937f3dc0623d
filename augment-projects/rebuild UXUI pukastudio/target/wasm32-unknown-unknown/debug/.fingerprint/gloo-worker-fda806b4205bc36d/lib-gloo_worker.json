{"rustc": 5357548097637079788, "features": "[\"default\"]", "declared_features": "[\"default\", \"futures\"]", "target": 11217130877511374870, "profile": 15657897354478470176, "path": 9243685073002756712, "deps": [[65234016722529558, "bincode", false, 11302470809687920160], [2706460456408817945, "futures", false, 14100822081600768853], [5921074888975346911, "gloo_utils", false, 11478267699244409448], [6946689283190175495, "wasm_bindgen", false, 8015492438657511113], [8008191657135824715, "thiserror", false, 2278106567464479382], [8264480821543757363, "web_sys", false, 7600967520588414932], [9003359908906038687, "js_sys", false, 16701719607648578680], [9689903380558560274, "serde", false, 9556902677958282247], [12919484467758300183, "gloo_worker_macros", false, 2391830015495101826], [14997362514532562728, "pinned", false, 7435639449808406504], [15917073803248137067, "wasm_bindgen_futures", false, 17074736512650298747]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-worker-fda806b4205bc36d/dep-lib-gloo_worker", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}