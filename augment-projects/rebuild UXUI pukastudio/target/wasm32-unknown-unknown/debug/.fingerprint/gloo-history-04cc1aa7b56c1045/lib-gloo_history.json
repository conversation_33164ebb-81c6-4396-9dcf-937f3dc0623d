{"rustc": 5357548097637079788, "features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "declared_features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "target": 7162787421944370751, "profile": 15657897354478470176, "path": 10987214441416679720, "deps": [[6946689283190175495, "wasm_bindgen", false, 8015492438657511113], [8008191657135824715, "thiserror", false, 2278106567464479382], [8264480821543757363, "web_sys", false, 14485297427298658205], [9689903380558560274, "serde", false, 9556902677958282247], [10542362878077003840, "serde_wasm_bindgen", false, 5148902940091449513], [12069273292196623910, "gloo_events", false, 13682637081122952523], [16542808166767769916, "serde_urlencoded", false, 13189874160744408783], [18122101786498147437, "gloo_utils", false, 3478830328554578998]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-history-04cc1aa7b56c1045/dep-lib-gloo_history", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}