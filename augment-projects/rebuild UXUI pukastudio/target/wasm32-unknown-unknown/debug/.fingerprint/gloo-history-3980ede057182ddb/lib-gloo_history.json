{"rustc": 5357548097637079788, "features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "declared_features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "target": 17962624438068670222, "profile": 15657897354478470176, "path": 10780843913085627624, "deps": [[4106074096146763267, "gloo_events", false, 11514216196794859130], [5921074888975346911, "gloo_utils", false, 11478267699244409448], [6946689283190175495, "wasm_bindgen", false, 8015492438657511113], [8008191657135824715, "thiserror", false, 2278106567464479382], [8264480821543757363, "web_sys", false, 7600967520588414932], [9689903380558560274, "serde", false, 9556902677958282247], [9920160576179037441, "getrandom", false, 17468616894214455987], [11261232116272131900, "serde_wasm_bindgen", false, 3754411029884218381], [16542808166767769916, "serde_urlencoded", false, 13189874160744408783]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-history-3980ede057182ddb/dep-lib-gloo_history", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}