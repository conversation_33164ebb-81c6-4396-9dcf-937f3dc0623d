{"rustc": 5357548097637079788, "features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "declared_features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "target": 7162787421944370751, "profile": 15657897354478470176, "path": 10987214441416679720, "deps": [[6946689283190175495, "wasm_bindgen", false, 8015492438657511113], [8008191657135824715, "thiserror", false, 2278106567464479382], [8264480821543757363, "web_sys", false, 7600967520588414932], [9689903380558560274, "serde", false, 9556902677958282247], [10542362878077003840, "serde_wasm_bindgen", false, 5148902940091449513], [12069273292196623910, "gloo_events", false, 9402876593659975256], [16542808166767769916, "serde_urlencoded", false, 13189874160744408783], [18122101786498147437, "gloo_utils", false, 13631161158373584015]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-history-706ac8ba5dbc8345/dep-lib-gloo_history", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}