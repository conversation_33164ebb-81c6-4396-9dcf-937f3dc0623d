{"rustc": 5357548097637079788, "features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"futures\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "declared_features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"futures\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "target": 15424014265002599538, "profile": 15657897354478470176, "path": 17076279696429071933, "deps": [[932682457716462887, "gloo_worker", false, 16791504838030271725], [3019852804355383212, "gloo_net", false, 13682274188208692509], [4106074096146763267, "gloo_events", false, 11514216196794859130], [5921074888975346911, "gloo_utils", false, 11478267699244409448], [10534580474014942609, "gloo_dialogs", false, 3972158082795772361], [12805895089852270265, "gloo_timers", false, 423489483470355721], [13555662950551470143, "gloo_storage", false, 1742058544306140873], [14803534212725985479, "gloo_file", false, 8797477012410259186], [15525454374806154151, "gloo_render", false, 4501870079718433024], [16754746302422839792, "gloo_console", false, 11226623359625751808], [17123018067225436055, "gloo_history", false, 9677753023507098896]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-61e6fc6906e85452/dep-lib-gloo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}