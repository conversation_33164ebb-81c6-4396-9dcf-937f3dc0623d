{"rustc": 5357548097637079788, "features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "declared_features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"futures\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "target": 15424014265002599538, "profile": 15657897354478470176, "path": 12314086409316626827, "deps": [[4106074096146763267, "gloo_events", false, 7354046426735648129], [5921074888975346911, "gloo_utils", false, 398264950662632977], [7877719575965225529, "gloo_net", false, 9579894516891176644], [10534580474014942609, "gloo_dialogs", false, 6564510354104511924], [12805895089852270265, "gloo_timers", false, 423489483470355721], [13514182243784546128, "gloo_worker", false, 14717099365462091644], [13555662950551470143, "gloo_storage", false, 5931909281730443167], [14803534212725985479, "gloo_file", false, 11536134026745261185], [15525454374806154151, "gloo_render", false, 12836389372917595780], [16754746302422839792, "gloo_console", false, 5463907296046442352], [17123018067225436055, "gloo_history", false, 9453481293172412192]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/gloo-359de495e675f43d/dep-lib-gloo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}