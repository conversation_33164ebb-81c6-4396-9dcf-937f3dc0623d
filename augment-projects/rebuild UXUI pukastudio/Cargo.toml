[package]
name = "puka-studio"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
yew = { version = "0.21", features = ["csr"] }
yew-router = "0.18"
web-sys = { version = "0.3", features = [
  "console",
  "Document",
  "Element",
  "HtmlElement",
  "HtmlInputElement",
  "HtmlSelectElement",
  "HtmlTextAreaElement",
  "Location",
  "Url",
  "UrlSearchParams",
  "Window",
  "CssStyleDeclaration",
  "DomTokenList",
  "MediaQueryList",
  "Storage",
  "Event",
  "EventListener",
] }
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
js-sys = "0.3"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde", "wasmbind"] }
gloo = "0.11"
gloo-net = "0.5"
gloo-storage = "0.3"
gloo-timers = "0.3"
uuid = { version = "1.0", features = ["v4", "serde", "js"] }
log = "0.4"
wasm-logger = "0.2"
yew-hooks = "0.3.3"

[dependencies.getrandom]
version = "0.2"
features = ["js"]

[profile.release]
lto = true
opt-level = "s"
codegen-units = 1
panic = "abort"
